/* 滚动容器必须设置固定高度 */
.scrollarea {
  width: 100vw;
  height: 100vh;
  /* 高度占满屏幕，确保能滚动 */
  position: relative;
  pointer-events: auto;
  color: white;
}

/* 背景图设置（高度需超过 scrollarea 高度才会滚动） */
.bg_image {
  width: 100%;
}

.btn_rule {
  position: absolute;
  top: 268rpx;
  right: 0;
  width: 52rpx;
  height: 140rpx;
  z-index: 1;
}

.btn_gift {
  position: absolute;
  top: 420rpx;
  right: 0;
  width: 52rpx;
  height: 140rpx;
  z-index: 1;
}

.content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.login {
  margin-top: 973rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.username {
  font-size: 28rpx;
  margin-bottom: 25rpx;
}

.award_group1 {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 74rpx;
}

.a_l,
.a_r {
  width: 318rpx;
  height: 407rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.a_l_bg {
  width: 318rpx;
  height: 310rpx;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.alr{
  width: 135rpx;
  height: 118rpx;
  position: absolute;
  top: 63rpx;
  left: 93rpx;
  z-index: 10;
}

.m_c {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 50rpx;
}

.c1 .tit {
  width: 692rpx;
  height: 63rpx;
}

.i_c {
  width: 742rpx;
  height: 492rpx;
}

.c1 {
  width: 100vw;
  height: 526rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.c1 .tit{
  
}

.btn_gamejr {
  display: flex;
  justify-content: center;
  align-content: center;
}

.btn_gamejr image {
  width: 342rpx;
  height: 119rpx;
}

.award_group2 {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 67rpx;
  margin-bottom: 33rpx;
}

.al,
.rt {
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
}

.dzg_video,
.dzg_office {
  width: 318rpx;
  height: 341rpx;
}

.l_konw{
  width: 281rpx;
  height: 100rpx;
}

.dzg_gz {
  width: 242rpx;
  height: 87rpx;
}
.pop{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
/* 隐私 */
.y_s {
  width: 100%;
  text-align: center;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  position: relative;
}
.y_s::after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 280rpx;
  width: 195rpx;
  height: 1rpx;
  background: white;
}
.ys_top{
  width: 534rpx;
  height: 273rpx;
  margin-bottom: 35rpx;
  transform: translateY(-45rpx);
}
.ys_tap{
  width: 535rpx;
  height: 127rpx;
  font-size: 26rpx;
  font-weight: 500;
  background-color: #765af7;
  display:flex;
  flex-direction: column;
  align-items: flex-start;
  border-radius:15rpx;
  border:1rpx solid white;
  transform: translateY(-45rpx);
  padding: 10rpx 0;
  box-sizing: border-box;
}
/* 隐私文本 */
.ystxt{
  /* text-decoration: underline white; */
  position: relative;
}
.ystxt::after {
  content: '';
  position: absolute;
  bottom:0;
  left: 15rpx;
  width: 95%;
  height: 1rpx;
  background: white;
}

/* bingding */
.section {
  width: 455rpx;
  height: 180rpx;
  margin-bottom: 35rpx;
  margin-top: 50rpx;
}

.btn_comfirm {
  width: 241rpx;
  height: 86rpx;
}

.rule_list{
  width: auto;
  min-width: 534rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.pop_clo{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/*二维码 */
.gz_m{
  width: 170rpx;
  height: 170rpx;
}
.gz_txt{
  margin-top: 30rpx;
}
.gz_middle{
  width: 445rpx;
  height: 313rpx;
  margin: 15rpx 0 25rpx 0;
}
.pop_gz{
  font-size: 24rpx;
}

.assist_img{
  width: 216rpx;
  height: 241rpx;
  margin:16rpx 0 ;
}
.assist_text{
  margin-top: 25rpx;
}
.assist_button{
  width: 281rpx;
  height: 100rpx;
}

.zl_num{
  font-size: 22rpx;
}

.share_container{
  width: 242rpx;
  height: 87rpx;
  position: relative;
}

.share_btn{
  position: absolute;
  top: 0;
  left: 0;
  width: 242rpx !important;
  height: 87rpx !important;
  opacity: 0;
  padding: 0;
  margin: 0;
  border:0;
  background-color: none;
}
.share_image{
  position: absolute;
  top: 0;
  left: 0;
  width: 242rpx;
  height: 87rpx;
}

.btn_jryx {
  width: 242rpx;
  height: 87rpx;
  /* position: absolute;
  top: 330rpx;
  left: 28rpx; */
}
.ar_pos{
  height: 318rpx;
  width: 318rpx;
  position: relative;
}
