Component({
  properties: {
    options: { type: Array, value: [] },           // [{label, value}]
    value: { type: String, optionalTypes:[Number], value: '' },
    placeholder: { type: String, value: '请选择' },
    disabled: { type: Boolean, value: false },
    maxHeight: { type: Number, value: 360 }        
  },
  data: { open: false, selectedLabel: '' },
  observers: {
    'value, options': function (v, opts) {
      const m = (opts || []).find(o => o.value === v)
      this.setData({ selectedLabel: m ? m.label : '' })
    }
  },
  methods: {
    noop() {},
    toggle() {
      if (this.properties.disabled) return
      this.setData({ open: !this.data.open })
    },
    close() { this.setData({ open: false }) },
    onSelect(e) {
      const { value, label } = e.currentTarget.dataset
      this.setData({ open: false, selectedLabel: label })
      this.triggerEvent('change', { value, label })
    }
  }
})
