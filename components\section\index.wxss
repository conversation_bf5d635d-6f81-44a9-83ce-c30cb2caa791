.select { position: relative; width: 100%; }

.select-box {
  height: 72rpx;
  padding: 0 24rpx;
  border-radius: 12rpx;
  background: #66baff;
  display: flex; align-items: center; justify-content: space-between;
  color: #fff; font-size: 28rpx;
}
.select-box.disabled { opacity: 0.5; }

.select-arrow { width: 28rpx; height: 28rpx; transition: transform .2s; }
.select-arrow.up { transform: rotate(180deg); }

.dropdown {
  position: absolute; left: 0; right: 0; top: calc(100% + 8rpx);
  background: #fff; border-radius: 12rpx; box-shadow: 0 12rpx 36rpx rgba(0,0,0,.18);
  z-index: 1001; overflow: hidden;
}

.option { padding: 24rpx; font-size: 28rpx; color: #333; }
.option + .option { border-top: 1rpx solid #eee; }
.option.active { background: #e6f4ff; color: #1677ff; font-weight: 600; }

.mask {
  position: fixed; inset: 0; background: transparent; z-index: 1000;
}
