<view class="cascader">
  <!-- 渠道按钮 -->
  <picker mode="selector" range="{{channels}}" range-key="label" bindchange="onChannelChange">
    <view class="btn">
      {{selectedChannel.label || '请选择渠道：'}}
      <text class="arrow">▼</text>
    </view>
  </picker>

  <!-- 系统按钮：始终显示，如果没有数据则显示提示 disabled="{{systems.length === 0}}"-->
  <picker mode="selector" range="{{systems}}" range-key="label" bindchange="onSystemChange" >
    <view class="btn {{systems.length === 0 ? 'btn-disabled' : ''}}">
      {{selectedSystem.label || '请选择系统：'}}
      <text class="arrow">▼</text>
    </view>
  </picker>

  <!-- 角色按钮：始终显示，如果没有数据则显示提示 disabled="{{roles.length === 0}}" -->
  <picker mode="selector" range="{{roles}}" range-key="label" bindchange="onRoleChange" >
    <view class="btn {{roles.length === 0 ? 'btn-disabled' : ''}}">
      {{selectedRole.label || '请选择角色：'}}
      <text class="arrow">▼</text>
    </view>
  </picker>
</view>
