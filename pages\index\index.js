// const plugin = requirePlugin('qq-wxmini-plugin');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    username: '',
    isInvite: false,
    isSub: false,
    gitfData: [],
    currentGz: 'sph',
    popupVisible: false,
    currentPopupType: '', // 当前显示的弹窗类型
    // 弹窗配置列表：每个对象包含 type、背景图、内容配置
    popupConfigs: [{
        type: 'bind', // 绑定弹窗
        bgImg: '/images/modal/pop6.png',
        content: {
          type: 'form',
          cascaderData: [{
              label: '渠道A',
              value: 'channelA',
              children: [{
                  label: 'iOS',
                  value: 'ios',
                  children: [{
                      label: '角色1',
                      value: 'role1'
                    },
                    {
                      label: '角色2',
                      value: 'role2'
                    }
                  ]
                },
                {
                  label: 'Android',
                  value: 'android',
                  children: [{
                    label: '角色3',
                    value: 'role3'
                  }]
                }
              ]
            },
            {
              label: '渠道B',
              value: 'channelB',
              children: [{
                label: 'iOS',
                value: 'ios',
                children: [{
                  label: '角色X',
                  value: 'roleX'
                }]
              }]
            }
          ]
        }
      },
      {
        type: 'rule', // 规则弹窗
        bgImg: '/images/modal/pop4.png',
        content: {
          type: 'textList',
          list: ['一、活动规则内容', '二、活动规则内容', '三、活动规则内容']
        }
      },
      {
        type: 'gift', // 奖励弹窗
        bgImg: '/images/modal/pop2.png',
        content: {
          type: 'reward',
          tip: '奖励请进入绑定游戏账号的游戏内邮箱查收'
        }
      },
      {
        type: 'ys', // 隐私弹窗
        bgImg: '/images/modal/pop9.png',
        content: {
          type: 'privacy',
          topImg: '/images/modal/ys-top.png',
          texts: ['《欢乐斗地主许可及服务协议》', '《欢乐斗地主资讯站小程序隐私政策》'

          ]
        }
      },
      {
        type: 'gz', // 关注弹窗（公众号/视频号）
        bgImg: '/images/modal/pop5.png',
        content: {
          type: 'follow', // 内容类型
          gzh: { // 公众号配置
            title: '关注欢乐斗地主官方公众号',
            img: '/images/modal/gzh.png'
          },
          sph: { // 视频号配置
            title: '关注欢乐斗地主官方视频号',
            img: '/images/modal/sph.png'
          },
          qrCodeImg: '/images/modal/m.png' // 二维码图片
        }
      },
      {
        type: 'zlcg', // 助力成功
        bgImg: '/images/modal/pop3.png',
        content: {
          type: 'assistCg',
          img: '/images/modal/j1.png',
          text: '已为你的好友助力成功',
          buttonImg: '/images/modal/l_konw.png'
        }
      },
      {
        type: 'zlsb', // 助力失败
        bgImg: '/images/modal/pop1.png',
        content: {
          type: 'assistSb',
          img: '/images/modal/j2.png',
          text: '已为你的好友助力成功',
          buttonImg: '/images/modal/l_konw.png'
        }
      },
      {
        type: 'zlyq', // 助力邀请
        bgImg: '/images/modal/pop7.png',
        content: {
          type: 'assistYq',
          img: '/images/modal/j3.png',
          text: '你的好友求你帮Ta助力领奖',
          buttonImg: '/images/modal/btn-zl.png'
        }
      },
      {
        type: 'zltrcg', // 助力成功
        bgImg: '/images/modal/pop3.png',
        content: {
          type: 'assistTrcg',
          img: '/images/modal/j4.png',
          text: '已为你的好友助力成功',
          buttonImg: '/images/modal/l_konw.png'
        }
      },
      {
        type: 'dl', // 登录
        bgImg: '/images/modal/pop_login.png',
        content: {
          type: 'loginTip',
          texts: ['今日还未登录游戏，', '请登录后再为您的好友助力哦！'],
          img: '/images/modal/l_konw.png'
        }
      }
    ]
  },
  showPopup(type) {
    const config = this.data.popupConfigs.find(item => item.type === type);
    if (!config) {
      console.error(`没找到 ${type} 的弹窗配置`);
      return;
    }
    this.setData({
      popupVisible: true,
      currentPopupType: type,
      currentConfig: config
    });
  },

  // 关闭弹窗
  handleClose() {
    this.setData({
      popupVisible: false
    });
  },
  // 绑定确定按钮
  onConfirm() {
    const cascader = this.selectComponent('#myCascader');
    const selection = cascader.getSelection();
    console.log('当前选择结果：', selection);
  },
  //  绑定角色
  handleBind() {
    this.showPopup('bind')
  },
  //  活动规则弹窗
  handleRule() {
    this.showPopup('rule');
  },
  // 领奖记录
  handleGift() {
    this.showPopup('gift');
    // TODO:来
    // this.showPopup('zlyq');
  },
  // 隐私协议保护协议
  handleYs() {
    this.showPopup('ys');
  },
  handleL1() {
    this.setData({
      isInvite: !this.data.isInvite
    })
  },
  handleL2() {
    this.setData({
      isSub: !this.data.isSub
    })
  },
  onAgreementTap(e) {
    const index = e.currentTarget.dataset.index;
    // 根据索引判断点击的是哪个协议
    if (index === 0) {
      this.serviceAgreement();
    } else if (index === 1) {
      this.privacyAgreement();
    }
  },
  // 服务协议
  serviceAgreement() {
    /*https://hlddz.qq.com/act/a20211028transit/index.html?type=1&channelId=999001 */
    this.jumpWebView('https://hlddz.qq.com/act/a20211028transit/index.html?type=1&channelId=999001')
    this.setData({popupVisible:false})
  },
  // 隐私协议
  privacyAgreement() {
    /*https://rule.tencent.com/rule/preview/2c62e295-d089-491f-b084-040329ab3a20 */
    this.jumpWebView('https://rule.tencent.com/rule/preview/2c62e295-d089-491f-b084-040329ab3a20')
    this.setData({popupVisible:false})
  },
  jumpWebView(url) {
    wx.navigateTo({
        url: '/pages/jump/index?url=' + encodeURIComponent(url)
    })
  },
  // 最下面进入游戏
  gameJr() {
    console.log(123)
  },
  // 视频--关注
  handleSphGz() {
    this.setData({
      currentGz: 'sph'
    })
    this.showPopup('gz')
  },
  handleGzhGz() {
    this.setData({
      currentGz: 'gzh'
    })
    this.showPopup('gz')
  },
   // 绑定下面进入游戏
   async jumpToGame() {
    if (!plugin.isQQEnv()) {
      const options = {
        appId: 'wx4bfd324d7e80de2e',
        path: 'packageGame/pages/launchgamecenter/index',
        extraData: {
          appId: 'wx76fc280041c16519',
          passData: JSON.stringify({
            type: 1,
          })
        },
      };
      wx.navigateToMiniProgram(options);
    } else {
      plugin.showModal({
        title: '提示',
        content: '即将离开QQ，打开「欢乐斗地主」',
        success: (res) => {
          if (res.confirm) {
            let jumpUrl = "https://speed.gamecenter.qq.com/pushgame/v1/detail?appid=363";
            plugin.openUrl({
              url: jumpUrl,
            });
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    }
  },
  // 分享
onShareAppMessage: function() {
  return {
    title: '欢乐斗地主精彩活动',
    path: '/pages/index/index',
    imageUrl: '/images/bg.jpg'
  }
},

// 朋友圈分享
onShareTimeline: function() {
  return {
    title: '欢乐斗地主精彩活动',
    query: 'from=timeline',
    imageUrl: '/images/bg.jpg'
  }
},
  onLoad(options) {
    this.setData({
      currentPopupType: 'bind',
      popupVisible: false
    });
  }
})