<!-- 只保留 scroll-view 作为滚动容器，内部直接放背景图和内容 -->
<scroll-view class="scrollarea" scroll-y>
  <image src="/images/bg.jpg" mode="widthFix" class="bg_image" />
  <!-- 活动规则 -->
  <image bind:tap="handleRule" src="/images/btn-rule.png" mode="widthFix" class="btn_rule" bindtap="handleRule" />
  <!-- 领奖记录 -->
  <image src="/images/btn-gift.png" mode="widthFix" class="btn_gift" bindtap="handleGift" />
  <!-- 内容列表 -->
  <view class="content">
    <!-- 绑定绝色 -->
    <view class="login">
      <text bind:tap='handleBind' class="username">你好,{{username?username:'请，【绑定角色】'}}</text>
    </view>
    <view class="award_group1">
      <view class="a_l">
        <view class="ar_pos">
          <image src="/images/award1.png" mode="widthFix" class="a_l_bg" bind:tap="handleL1" />
          <!-- 已领取 -->
          <image wx:if="{{isInvite}}" src="/images/alr_.png" mode="widthFix" class="alr" />
        </view>
        <view class="share_container"><image class="share_image" src="/images/btn-share.png" mode="widthFix" />
        <button open-type="share" class="share_btn"></button>
          
        </view>
      </view>
      <view class="a_r">
        <view class="ar_pos">
          <image src="/images/award2.png" mode="widthFix" class="a_l_bg" bind:tap="handleL2" />

        <!-- 已领取 -->
        <image wx:if="{{isSub}}" src="/images/alr_.png" mode="widthFix" class="alr" />
        </view>
        <view class="share_container">
        <image class="btn_jryx" src="/images/btn-jryx.png" mode="scaleToFill" bindtap="jumpToGame" />
        </view>

        <!-- <image class="btn_share" src="/images/btn-jryx.png" mode="scaleToFill" bindtap="jumpToGame" /> -->
      </view>
    </view>
    <view class="m_c">
      <view class="c1">
        <view class="tit">
          <image src="/images/tit1.png" style="width: 692rpx;height: 63rpx;" mode="widthFix" class="tit1" />
        </view>
        <view class="con">
          <image src="/images/con1.png" style="width: 742rpx;height: 492rpx;" mode="scaleToFill" class="c1_con1" />
        </view>
      </view>
      <view class="c1">
        <view class="tit">
          <image src="/images/tit3.png" style="width: 682rpx;height: 63rpx;" mode="widthFix" />
        </view>
        <view class="con">
          <image src="/images/con1.png" style="width: 742rpx;height: 492rpx;" mode="widthFix" />
        </view>
      </view>
      <view class="c1">
        <view class="tit">
          <image src="/images/tit4.png" style="width: 682rpx;height: 63rpx;" mode="widthFix" />
        </view>
        <view class="con">
          <image src="/images/con1.png" style="width: 742rpx;height: 492rpx;" mode="widthFix" />
        </view>
      </view>
      <view class="c1">
        <view class="tit">
          <image src="/images/tit5.png" style="width: 682rpx;height: 63rpx;" mode="widthFix" class="" />
        </view>
        <view class="con">
          <image src="/images/con1.png" style="width: 742rpx;height: 492rpx;" mode="widthFix" class="" />
        </view>
      </view>
      <view class="c1">
        <view class="tit">
          <image src="/images/tit6.png" style="width: 682rpx;height: 63rpx;" mode="widthFix" />
        </view>
        <view class="con">
          <image src="/images/con1.png" style="width: 742rpx;height: 492rpx;" mode="widthFix" />
        </view>
      </view>
      <view class="c1">
        <view class="tit">
          <image src="/images/tit7.png" style="width: 682rpx;height: 63rpx;" mode="widthFix" class="" />
        </view>
        <view class="con">
          <image src="/images/con1.png" style="width: 742rpx;height: 492rpx;" mode="widthFix" class="" />
        </view>
      </view>
      <view class="c1">
        <view class="tit">
          <image src="/images/tit8.png" style="width: 682rpx;height: 63rpx;" mode="widthFix" />
        </view>
        <view class="con">
          <image src="/images/con1.png" style="width: 742rpx;height: 492rpx;" mode="widthFix" />
        </view>
      </view>
    </view>
    <view class="award_group2">
      <view class="al">
        <image src="/images/gz1.png" mode="widthFix" class="dzg_video" />
        <image src="/images/btn-gz.png" mode="widthFix" class="dzg_gz" bindtap="handleSphGz" />
      </view>
      <view class="rt">
        <image src="/images/gz2.png" mode="widthFix" class="dzg_office" />
        <image src="/images/btn-gz.png" mode="widthFix" class="dzg_gz" bindtap="handleGzhGz" />
      </view>
    </view>
    <view class="y_s" bind:tap="handleYs">隐私协议保护指南</view>
    <view class="btn_gamejr">
      <image src="/images/btn-gamejr.png" mode="widthFix" bindtap="jumpToGame" />
    </view>
  </view>


  <popup visible="{{popupVisible}}" bgImg="{{currentConfig ? currentConfig.bgImg : ''}}" bind:cancel="handleClose">
    <!-- 绑定 -->
    <view class="pop_clo">
      <view wx:if="{{currentConfig.content.type === 'form'}}" class="pop">
        <view class="section">
          <cascader id="myCascader" data="{{currentConfig.content.cascaderData}}"></cascader>
        </view>
        <image src="/images/modal/btn-confrim.png" mode="widthFix" class="btn_comfirm" bind:tap="onConfirm" />
      </view>
      <!-- 活动规则 -->
      <view wx:elif="{{currentConfig.content.type === 'textList'}}">
        <view class="rule_list">
          <block wx:for="{{currentConfig.content.list}}" wx:key="index">
            <text>{{item}}</text>
          </block>
        </view>
      </view>
      <!-- 奖励记录 -->
      <view wx:elif="{{currentConfig.content.type === 'reward'}}">
        <view>{{gitfData.length <= 0 ? '暂无奖励记录' : '奖励已发放'}}</view>
        <view>{{gitfData.length > 0 ? currentConfig.content.tip : ''}}</view>
      </view>
      <!-- 隐私 -->
      <view wx:elif="{{currentConfig.content.type === 'privacy'}}">
        <image src="{{currentConfig.content.topImg}}" mode="widthFix" class="ys_top" />
        <view class="ys_tap">
          <block wx:for="{{currentConfig.content.texts}}" wx:key="index">
            <text style="margin:5rpx 0 10rpx 0;" class="ystxt" bind:tap="onAgreementTap" data-index="{{index}}">{{item}}</text>
          </block>
        </view>
      </view>
      <!-- 关注 -->
      <view wx:elif="{{currentConfig.content.type === 'follow'}}" class="pop_gz pop">
        <view class="gz_txt ">
          {{currentGz === "gzh" 
          ? currentConfig.content.gzh.title 
          : currentConfig.content.sph.title}}
        </view>
        <image class="gz_middle" src="{{currentGz === 'gzh' ? currentConfig.content.gzh.img : currentConfig.content.sph.img}}" mode="" />
        <image src="{{currentConfig.content.qrCodeImg}}" mode="widthFix" class="gz_m" show-menu-by-longpress="true" />
      </view>
      <!-- 助力 -->
      <view wx:elif="{{currentConfig.content.type === 'assistCg' || currentConfig.content.type === 'assistSb' || currentConfig.content.type === 'assistYq' || currentConfig.content.type === 'assistTrcg'}}" class="pop_assist pop">
        <text class="assist_text">{{currentConfig.content.text}}</text>
        <image src="{{currentConfig.content.img}}" mode="widthFix" class="assist_img" />
        <image src="{{currentConfig.content.buttonImg}}" mode="widthFix" class="assist_button" bind:tap="handleClose" />
        <text class="zl_ch">(可助力3/3次)</text>
      </view>

      <!-- 7. 登录提示 -->
      <view wx:elif="{{currentConfig.content.type === 'loginTip'}}">
        <text>{{currentConfig.content.texts[0]}}</text>
        <text>{{currentConfig.content.texts[1]}}</text>
        <image src="{{currentConfig.content.img}}" mode="widthFix" class="l_konw" />
      </view>
    </view>
  </popup>
</scroll-view>