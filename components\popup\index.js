Component({
  properties: {
    visible: { type: Boolean, value: false },
    title: { type: String, value: '' },
    confirmText: { type: String, value: '确定' },
    bgImg: { type: String, value: '' },
    closeIcon: { type: String, value: '/images/modal/btn-close.png' },
    maxWidth: { type: Number, value: 600 } // 最大宽度 (rpx)，可调
  },
  data: {
    popupWidth: 0,
    popupHeight: 0
  },
  methods: {
    stopTap() {},
    onMaskClick() {}, // 不关闭
    onCancel() { this.triggerEvent('cancel') },
    onConfirm() { this.triggerEvent('confirm') },
    onBgLoad(e) {
      const { width, height } = e.detail;
      const maxW = this.properties.maxWidth;

      // 原图比例换算成 rpx 尺寸
      const ratio = height / width;
      const w = maxW;
      const h = Math.floor(w * ratio);

      this.setData({
        popupWidth: w,
        popupHeight: h
      });
    }
  }
})
