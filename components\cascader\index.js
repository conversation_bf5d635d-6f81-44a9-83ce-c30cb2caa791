Component({
  properties: {
    data: {
      type: Array,
      value: []
    }
  },
  data: {
    channels: [],
    systems: [],
    roles: [],
    selectedChannel: {},
    selectedSystem: {},
    selectedRole: {}
  },
  lifetimes: {
    attached() {
      this.setData({
        channels: this.data.data || []
      });
    }
  },
  methods: {
    resetSelection() {
      this.setData({
        selectedChannel: {},
        selectedSystem: {},
        selectedRole: {},
        systems: [],
        roles: []
      });
    },
    getSelection() {
      return {
        channel: this.data.selectedChannel,
        system: this.data.selectedSystem,
        role: this.data.selectedRole
      };
    },
    onChannelChange(e) {
      const index = e.detail.value;
      const channel = this.data.channels[index];
      this.setData({
        selectedChannel: channel,
        systems: channel.children || [],
        roles: [],
        selectedSystem: {},
        selectedRole: {}
      });
    },
    onSystemChange(e) {
      const index = e.detail.value;
      const system = this.data.systems[index];
      this.setData({
        selectedSystem: system,
        roles: system.children || [],
        selectedRole: {}
      });
    },
    onRoleChange(e) {
      const index = e.detail.value;
      const role = this.data.roles[index];
      this.setData({
        selectedRole: role
      });
    },
    // 对外提供的方法：获取当前选择信息
    getSelection() {
      return {
        channel: this.data.selectedChannel,
        system: this.data.selectedSystem,
        role: this.data.selectedRole
      };
    },
    // 内部触发事件给父组件
    confirmSelection() {
      this.triggerEvent('confirm', this.getSelection());
    }
  }
});
