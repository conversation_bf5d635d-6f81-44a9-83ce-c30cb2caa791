<view class="select" catchtap="noop">
  <!-- 选择框 -->
  <view class="select-box {{disabled ? 'disabled' : ''}}" bindtap="toggle">
    <text class="select-text">{{ selectedLabel || placeholder }}</text>
  </view>

  <!-- 下拉面板 + 点击外部关闭的遮罩 -->
  <view wx:if="{{open}}">
    <view class="dropdown">
      <scroll-view scroll-y style="max-height: {{maxHeight}}rpx;">
        <block wx:for="{{options}}" wx:key="value">
          <view
            class="option {{item.value === value ? 'active' : ''}}"
            data-value="{{item.value}}"
            data-label="{{item.label}}"
            bindtap="onSelect"
          >{{item.label}}</view>
        </block>
      </scroll-view>
    </view>
    <view class="mask" bindtap="close"></view>
  </view>
</view>
